/**
 * TUI Image Editor Monkey Patch
 * 直接修补TUI Image Editor内部方法以防止null引用错误
 */

class TuiEditorMonkeyPatch {
  constructor() {
    this.isPatched = false
    this.originalMethods = {}
    this.patchAttempts = 0
    this.maxPatchAttempts = 5
  }

  /**
   * 应用所有补丁
   * @param {Object} imageEditor - TUI Image Editor实例
   */
  applyPatches(imageEditor) {
    if (!imageEditor || this.isPatched) {
      return
    }

    console.log('开始应用TUI Image Editor补丁...')

    try {
      // 补丁1: 修复Cropper._onFabricMouseMove方法
      this.patchCropperMouseMove(imageEditor)
      
      // 补丁2: 修复Fabric.js事件处理器
      this.patchFabricEventHandlers(imageEditor)
      
      // 补丁3: 增强Cropper初始化
      this.patchCropperInitialization(imageEditor)
      
      // 补丁4: 修复Canvas事件绑定
      this.patchCanvasEventBinding(imageEditor)

      this.isPatched = true
      console.log('TUI Image Editor补丁应用成功')
    } catch (error) {
      console.error('应用TUI Image Editor补丁失败:', error)
      this.patchAttempts++
      
      // 如果补丁失败次数过多，停止尝试
      if (this.patchAttempts >= this.maxPatchAttempts) {
        console.error('补丁应用失败次数过多，停止尝试')
      }
    }
  }

  /**
   * 补丁1: 修复Cropper._onFabricMouseMove方法
   */
  patchCropperMouseMove(imageEditor) {
    try {
      // 尝试访问Cropper类
      const cropper = imageEditor._cropper
      if (!cropper) {
        console.warn('Cropper实例不存在，跳过鼠标移动补丁')
        return
      }

      // 保存原始方法
      if (cropper._onFabricMouseMove && !this.originalMethods.onFabricMouseMove) {
        this.originalMethods.onFabricMouseMove = cropper._onFabricMouseMove.bind(cropper)
      }

      // 创建安全的鼠标移动处理器
      cropper._onFabricMouseMove = function(fEvent) {
        try {
          // 安全检查：确保所有必要的对象都存在
          if (!fEvent || !fEvent.e) {
            return
          }

          // 检查cropper内部状态
          if (!this._canvas || !this._cropzone) {
            console.warn('Cropper内部对象缺失，跳过鼠标移动处理')
            return
          }

          // 检查cropzone的关键属性
          if (!this._cropzone.set || typeof this._cropzone.set !== 'function') {
            console.warn('Cropzone.set方法不存在，跳过鼠标移动处理')
            return
          }

          // 检查canvas状态
          if (!this._canvas.getPointer || typeof this._canvas.getPointer !== 'function') {
            console.warn('Canvas.getPointer方法不存在，跳过鼠标移动处理')
            return
          }

          // 安全地获取鼠标位置
          let pointer
          try {
            pointer = this._canvas.getPointer(fEvent.e)
          } catch (pointerError) {
            console.warn('获取鼠标位置失败:', pointerError)
            return
          }

          if (!pointer || typeof pointer.x !== 'number' || typeof pointer.y !== 'number') {
            console.warn('鼠标位置数据无效')
            return
          }

          // 安全地调用原始逻辑
          try {
            // 这里实现安全的cropzone更新逻辑
            if (this._cropzone && this._cropzone.set) {
              // 只有在所有条件都满足时才更新cropzone
              const currentLeft = this._cropzone.left || 0
              const currentTop = this._cropzone.top || 0
              
              // 计算新位置（简化版本，避免复杂计算导致的错误）
              const newLeft = Math.max(0, pointer.x - (this._cropzone.width || 100) / 2)
              const newTop = Math.max(0, pointer.y - (this._cropzone.height || 100) / 2)
              
              this._cropzone.set({
                left: newLeft,
                top: newTop
              })
              
              // 安全地重新渲染
              if (this._canvas && this._canvas.requestRenderAll) {
                this._canvas.requestRenderAll()
              }
            }
          } catch (updateError) {
            console.warn('更新cropzone失败:', updateError)
          }

        } catch (error) {
          console.error('安全鼠标移动处理器出错:', error)
          // 不抛出错误，避免中断用户操作
        }
      }.bind(cropper)

      console.log('Cropper鼠标移动方法补丁已应用')
    } catch (error) {
      console.error('应用Cropper鼠标移动补丁失败:', error)
    }
  }

  /**
   * 补丁2: 修复Fabric.js事件处理器
   */
  patchFabricEventHandlers(imageEditor) {
    try {
      const fabricCanvas = imageEditor._graphics?.getCanvas?.()
      if (!fabricCanvas) {
        console.warn('Fabric Canvas不存在，跳过事件处理器补丁')
        return
      }

      // 保存原始的事件处理方法
      if (fabricCanvas.__onMouseMove && !this.originalMethods.fabricMouseMove) {
        this.originalMethods.fabricMouseMove = fabricCanvas.__onMouseMove.bind(fabricCanvas)
      }

      // 创建安全的Fabric事件处理器
      fabricCanvas.__onMouseMove = function(e) {
        try {
          // 安全检查
          if (!e || !this._objects) {
            return
          }

          // 检查canvas状态
          if (!this.getPointer || !this.findTarget) {
            console.warn('Canvas方法缺失，跳过鼠标移动处理')
            return
          }

          // 调用原始方法（如果存在）
          if (this.originalMethods && this.originalMethods.fabricMouseMove) {
            try {
              this.originalMethods.fabricMouseMove(e)
            } catch (originalError) {
              console.warn('原始Fabric鼠标移动方法出错:', originalError)
            }
          }

        } catch (error) {
          console.error('安全Fabric鼠标移动处理器出错:', error)
        }
      }.bind(fabricCanvas)

      console.log('Fabric事件处理器补丁已应用')
    } catch (error) {
      console.error('应用Fabric事件处理器补丁失败:', error)
    }
  }

  /**
   * 补丁3: 增强Cropper初始化
   */
  patchCropperInitialization(imageEditor) {
    try {
      // 确保Cropper在初始化时有正确的状态
      const cropper = imageEditor._cropper
      if (cropper) {
        // 强制初始化关键属性
        if (!cropper._canvas) {
          cropper._canvas = imageEditor._graphics?.getCanvas?.()
        }

        // 确保cropzone存在
        if (!cropper._cropzone && cropper._canvas && window.fabric) {
          try {
            cropper._cropzone = new window.fabric.Rect({
              left: 50,
              top: 50,
              width: 100,
              height: 100,
              fill: 'transparent',
              stroke: '#ff0000',
              strokeWidth: 2,
              strokeDashArray: [5, 5],
              selectable: true,
              evented: true
            })
            
            if (cropper._canvas.add) {
              cropper._canvas.add(cropper._cropzone)
            }
          } catch (createError) {
            console.warn('创建默认cropzone失败:', createError)
          }
        }

        console.log('Cropper初始化补丁已应用')
      }
    } catch (error) {
      console.error('应用Cropper初始化补丁失败:', error)
    }
  }

  /**
   * 补丁4: 修复Canvas事件绑定
   */
  patchCanvasEventBinding(imageEditor) {
    try {
      const fabricCanvas = imageEditor._graphics?.getCanvas?.()
      if (!fabricCanvas) {
        return
      }

      // 确保canvas有正确的事件绑定状态
      if (!fabricCanvas._hasMouseEventListeners) {
        fabricCanvas._hasMouseEventListeners = true
      }

      // 重新绑定关键事件（如果缺失）
      if (!fabricCanvas._onMouseMove) {
        fabricCanvas._onMouseMove = function(e) {
          try {
            if (e && e.preventDefault) {
              e.preventDefault()
            }
          } catch (error) {
            // 忽略错误
          }
        }
      }

      console.log('Canvas事件绑定补丁已应用')
    } catch (error) {
      console.error('应用Canvas事件绑定补丁失败:', error)
    }
  }

  /**
   * 移除所有补丁
   */
  removePatches(imageEditor) {
    if (!this.isPatched || !imageEditor) {
      return
    }

    try {
      console.log('开始移除TUI Image Editor补丁...')

      // 恢复原始方法
      const cropper = imageEditor._cropper
      if (cropper && this.originalMethods.onFabricMouseMove) {
        cropper._onFabricMouseMove = this.originalMethods.onFabricMouseMove
      }

      const fabricCanvas = imageEditor._graphics?.getCanvas?.()
      if (fabricCanvas && this.originalMethods.fabricMouseMove) {
        fabricCanvas.__onMouseMove = this.originalMethods.fabricMouseMove
      }

      this.isPatched = false
      this.originalMethods = {}
      console.log('TUI Image Editor补丁已移除')
    } catch (error) {
      console.error('移除TUI Image Editor补丁失败:', error)
    }
  }

  /**
   * 检查是否需要重新应用补丁
   */
  checkAndReapplyPatches(imageEditor) {
    if (!imageEditor) {
      return
    }

    try {
      // 检查关键方法是否仍然是我们的安全版本
      const cropper = imageEditor._cropper
      if (cropper && cropper._onFabricMouseMove) {
        // 如果方法被重置，重新应用补丁
        const methodString = cropper._onFabricMouseMove.toString()
        if (!methodString.includes('安全检查') && !methodString.includes('安全地获取鼠标位置')) {
          console.log('检测到补丁被重置，重新应用...')
          this.isPatched = false
          this.applyPatches(imageEditor)
        }
      }
    } catch (error) {
      console.warn('检查补丁状态失败:', error)
    }
  }

  /**
   * 获取补丁状态
   */
  getPatchStatus() {
    return {
      isPatched: this.isPatched,
      patchAttempts: this.patchAttempts,
      hasOriginalMethods: Object.keys(this.originalMethods).length > 0
    }
  }
}

// 创建单例实例
const tuiEditorMonkeyPatch = new TuiEditorMonkeyPatch()

export default tuiEditorMonkeyPatch
